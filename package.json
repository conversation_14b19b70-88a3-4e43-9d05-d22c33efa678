{"name": "mcp-server-framework", "version": "independent", "description": "A comprehensive framework for building Model Context Protocol (MCP) servers", "private": true, "workspaces": ["packages/*"], "scripts": {"prepare": "husky", "test": "jest --passWithNoTests", "typecheck": "tsc --noEmit", "typecheck:test": "tsc --project tsconfig.test.json --noEmit", "lint": "biome check .", "lint:fix": "biome check . --write", "changeset": "changeset", "version-packages": "changeset version && changeset publish", "version:dry": "changeset version --snapshot dev && changeset status"}, "keywords": ["mcp", "model-context-protocol", "server", "framework"], "author": "", "license": "MIT", "packageManager": "pnpm@10.12.1", "lint-staged": {"*.{ts,tsx,js,jsx}": ["biome check --write --no-errors-on-unmatched"]}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@changesets/cli": "^2.29.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/jest": "^30.0.0", "@types/node": "^24.0.7", "husky": "^9.1.7", "jest": "^30.0.3", "lint-staged": "^16.1.2", "ts-jest": "^29.4.0", "typescript": "^5.8.3"}}