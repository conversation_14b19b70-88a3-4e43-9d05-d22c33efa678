hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.7':
    '@babel/compat-data': private
  '@babel/core@7.27.7':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.7)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.7':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.7)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.27.7)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.27.7)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.27.7)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.27.7)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.27.7)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.7':
    '@babel/traverse': private
  '@babel/types@7.27.7':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@biomejs/cli-darwin-arm64@2.0.6':
    '@biomejs/cli-darwin-arm64': private
  '@biomejs/cli-darwin-x64@2.0.6':
    '@biomejs/cli-darwin-x64': private
  '@biomejs/cli-linux-arm64-musl@2.0.6':
    '@biomejs/cli-linux-arm64-musl': private
  '@biomejs/cli-linux-arm64@2.0.6':
    '@biomejs/cli-linux-arm64': private
  '@biomejs/cli-linux-x64-musl@2.0.6':
    '@biomejs/cli-linux-x64-musl': private
  '@biomejs/cli-linux-x64@2.0.6':
    '@biomejs/cli-linux-x64': private
  '@biomejs/cli-win32-arm64@2.0.6':
    '@biomejs/cli-win32-arm64': private
  '@biomejs/cli-win32-x64@2.0.6':
    '@biomejs/cli-win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@30.0.2':
    '@jest/console': private
  '@jest/core@30.0.3':
    '@jest/core': private
  '@jest/diff-sequences@30.0.1':
    '@jest/diff-sequences': private
  '@jest/environment@30.0.2':
    '@jest/environment': private
  '@jest/expect-utils@30.0.3':
    '@jest/expect-utils': private
  '@jest/expect@30.0.3':
    '@jest/expect': private
  '@jest/fake-timers@30.0.2':
    '@jest/fake-timers': private
  '@jest/get-type@30.0.1':
    '@jest/get-type': private
  '@jest/globals@30.0.3':
    '@jest/globals': private
  '@jest/pattern@30.0.1':
    '@jest/pattern': private
  '@jest/reporters@30.0.2':
    '@jest/reporters': private
  '@jest/schemas@30.0.1':
    '@jest/schemas': private
  '@jest/snapshot-utils@30.0.1':
    '@jest/snapshot-utils': private
  '@jest/source-map@30.0.1':
    '@jest/source-map': private
  '@jest/test-result@30.0.2':
    '@jest/test-result': private
  '@jest/test-sequencer@30.0.2':
    '@jest/test-sequencer': private
  '@jest/transform@30.0.2':
    '@jest/transform': private
  '@jest/types@30.0.1':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.2.7':
    '@pkgr/core': private
  '@sinclair/typebox@0.34.37':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@13.0.5':
    '@sinonjs/fake-timers': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-android-arm-eabi@1.9.2':
    '@unrs/resolver-binding-android-arm-eabi': private
  '@unrs/resolver-binding-android-arm64@1.9.2':
    '@unrs/resolver-binding-android-arm64': private
  '@unrs/resolver-binding-darwin-arm64@1.9.2':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.9.2':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.9.2':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.9.2':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.9.2':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.9.2':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.9.2':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.9.2':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.9.2':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.9.2':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.9.2':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.9.2':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.9.2':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.9.2':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.9.2':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.9.2':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.9.2':
    '@unrs/resolver-binding-win32-x64-msvc': private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@5.2.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  argparse@1.0.10:
    argparse: private
  async@3.2.6:
    async: private
  babel-jest@30.0.2(@babel/core@7.27.7):
    babel-jest: private
  babel-plugin-istanbul@7.0.0:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@30.0.1:
    babel-plugin-jest-hoist: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.7):
    babel-preset-current-node-syntax: private
  babel-preset-jest@30.0.1(@babel/core@7.27.7):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  buffer-from@1.1.2:
    buffer-from: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001726:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  ci-info@4.2.0:
    ci-info: private
  cjs-module-lexer@2.1.0:
    cjs-module-lexer: private
  cliui@8.0.1:
    cliui: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  concat-map@0.0.1:
    concat-map: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cross-spawn@7.0.6:
    cross-spawn: private
  debug@4.4.1:
    debug: private
  dedent@1.6.0:
    dedent: private
  deepmerge@4.3.1:
    deepmerge: private
  detect-newline@3.1.0:
    detect-newline: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.177:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  error-ex@1.3.2:
    error-ex: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@2.0.0:
    escape-string-regexp: private
  esprima@4.0.1:
    esprima: private
  execa@5.1.1:
    execa: private
  exit-x@0.2.2:
    exit-x: private
  expect@30.0.3:
    expect: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fb-watchman@2.0.2:
    fb-watchman: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  find-up@4.1.0:
    find-up: private
  foreground-child@3.3.1:
    foreground-child: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-package-type@0.1.0:
    get-package-type: private
  get-stream@6.0.1:
    get-stream: private
  glob@10.4.5:
    glob: private
  globals@11.12.0:
    globals: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  html-escaper@2.0.2:
    html-escaper: private
  human-signals@2.1.0:
    human-signals: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-number@7.0.0:
    is-number: private
  is-stream@2.0.1:
    is-stream: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@5.0.6:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-changed-files@30.0.2:
    jest-changed-files: private
  jest-circus@30.0.3:
    jest-circus: private
  jest-cli@30.0.3(@types/node@24.0.7):
    jest-cli: private
  jest-config@30.0.3(@types/node@24.0.7):
    jest-config: private
  jest-diff@30.0.3:
    jest-diff: private
  jest-docblock@30.0.1:
    jest-docblock: private
  jest-each@30.0.2:
    jest-each: private
  jest-environment-node@30.0.2:
    jest-environment-node: private
  jest-haste-map@30.0.2:
    jest-haste-map: private
  jest-leak-detector@30.0.2:
    jest-leak-detector: private
  jest-matcher-utils@30.0.3:
    jest-matcher-utils: private
  jest-message-util@30.0.2:
    jest-message-util: private
  jest-mock@30.0.2:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@30.0.2):
    jest-pnp-resolver: private
  jest-regex-util@30.0.1:
    jest-regex-util: private
  jest-resolve-dependencies@30.0.3:
    jest-resolve-dependencies: private
  jest-resolve@30.0.2:
    jest-resolve: private
  jest-runner@30.0.3:
    jest-runner: private
  jest-runtime@30.0.3:
    jest-runtime: private
  jest-snapshot@30.0.3:
    jest-snapshot: private
  jest-util@30.0.2:
    jest-util: private
  jest-validate@30.0.2:
    jest-validate: private
  jest-watcher@30.0.2:
    jest-watcher: private
  jest-worker@30.0.2:
    jest-worker: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@3.14.1:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json5@2.2.3:
    json5: private
  leven@3.1.0:
    leven: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@5.0.0:
    locate-path: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lru-cache@5.1.1:
    lru-cache: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  makeerror@1.0.12:
    makeerror: private
  merge-stream@2.0.0:
    merge-stream: private
  micromatch@4.0.8:
    micromatch: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  ms@2.1.3:
    ms: private
  napi-postinstall@0.2.5:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@4.1.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  packages/core:
    '@mcp-server-framework/core': private
  parse-json@5.2.0:
    parse-json: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-scurry@1.11.1:
    path-scurry: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pretty-format@30.0.2:
    pretty-format: private
  pure-rand@7.0.1:
    pure-rand: private
  react-is@18.3.1:
    react-is: private
  require-directory@2.1.1:
    require-directory: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@5.0.0:
    resolve-from: private
  semver@7.7.2:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@4.1.0:
    signal-exit: private
  slash@3.0.0:
    slash: private
  source-map-support@0.5.13:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stack-utils@2.0.6:
    stack-utils: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  synckit@0.11.8:
    synckit: private
  test-exclude@6.0.0:
    test-exclude: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@4.41.0:
    type-fest: private
  undici-types@7.8.0:
    undici-types: private
  unrs-resolver@1.9.2:
    unrs-resolver: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  walker@1.0.8:
    walker: private
  which@2.0.2:
    which: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@5.0.1:
    write-file-atomic: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - unrs-resolver
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Sun, 29 Jun 2025 18:45:33 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@biomejs/cli-darwin-x64@2.0.6'
  - '@biomejs/cli-linux-arm64-musl@2.0.6'
  - '@biomejs/cli-linux-arm64@2.0.6'
  - '@biomejs/cli-linux-x64-musl@2.0.6'
  - '@biomejs/cli-linux-x64@2.0.6'
  - '@biomejs/cli-win32-arm64@2.0.6'
  - '@biomejs/cli-win32-x64@2.0.6'
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@napi-rs/wasm-runtime@0.2.11'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.9.2'
  - '@unrs/resolver-binding-android-arm64@1.9.2'
  - '@unrs/resolver-binding-darwin-x64@1.9.2'
  - '@unrs/resolver-binding-freebsd-x64@1.9.2'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.9.2'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.9.2'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.9.2'
  - '@unrs/resolver-binding-linux-arm64-musl@1.9.2'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.9.2'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.9.2'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.9.2'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.9.2'
  - '@unrs/resolver-binding-linux-x64-gnu@1.9.2'
  - '@unrs/resolver-binding-linux-x64-musl@1.9.2'
  - '@unrs/resolver-binding-wasm32-wasi@1.9.2'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.9.2'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.9.2'
  - '@unrs/resolver-binding-win32-x64-msvc@1.9.2'
  - tslib@2.8.1
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
