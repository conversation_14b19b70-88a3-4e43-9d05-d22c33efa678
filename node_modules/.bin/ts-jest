#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.27.7_@jest+transform@30.0.2_@jest+types@30.0.1_babel-jest@_abf915fd759a6962ddae92627e857f8e/node_modules/ts-jest/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.27.7_@jest+transform@30.0.2_@jest+types@30.0.1_babel-jest@_abf915fd759a6962ddae92627e857f8e/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.27.7_@jest+transform@30.0.2_@jest+types@30.0.1_babel-jest@_abf915fd759a6962ddae92627e857f8e/node_modules/ts-jest/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.27.7_@jest+transform@30.0.2_@jest+types@30.0.1_babel-jest@_abf915fd759a6962ddae92627e857f8e/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-jest/cli.js" "$@"
else
  exec node  "$basedir/../ts-jest/cli.js" "$@"
fi
