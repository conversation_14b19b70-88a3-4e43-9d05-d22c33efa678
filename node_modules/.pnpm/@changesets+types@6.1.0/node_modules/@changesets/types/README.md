# @changesets/types

[![npm package](https://img.shields.io/npm/v/@changesets/types)](https://npmjs.com/package/@changesets/types)
[![View changelog](https://img.shields.io/badge/Explore%20Changelog-brightgreen)](./CHANGELOG.md)

A package of types for use in changesets, or projects wishing to extend them.

Of these, the most useful types are:

## NewChangeset

The format for a changeset as of changesets version 2.

## ChangelogFunctions

The shape of the object used for generating changelog entries.

## ReleasePlan

The combined object used to version packages and update changelogs.
