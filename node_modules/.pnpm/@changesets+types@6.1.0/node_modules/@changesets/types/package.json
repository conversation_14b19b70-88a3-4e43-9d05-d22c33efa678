{"name": "@changesets/types", "version": "6.1.0", "description": "Common types shared between changeset packages", "main": "dist/changesets-types.cjs.js", "module": "dist/changesets-types.esm.js", "exports": {".": {"types": {"import": "./dist/changesets-types.cjs.mjs", "default": "./dist/changesets-types.cjs.js"}, "module": "./dist/changesets-types.esm.js", "import": "./dist/changesets-types.cjs.mjs", "default": "./dist/changesets-types.cjs.js"}, "./package.json": "./package.json"}, "license": "MIT", "repository": "https://github.com/changesets/changesets/tree/main/packages/types"}