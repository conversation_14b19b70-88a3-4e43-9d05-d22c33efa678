{"name": "@changesets/should-skip-package", "version": "0.1.2", "description": "Checks if a package should be skipped for versioning or tagging purposes", "main": "dist/changesets-should-skip-package.cjs.js", "module": "dist/changesets-should-skip-package.esm.js", "exports": {".": {"types": {"import": "./dist/changesets-should-skip-package.cjs.mjs", "default": "./dist/changesets-should-skip-package.cjs.js"}, "module": "./dist/changesets-should-skip-package.esm.js", "import": "./dist/changesets-should-skip-package.cjs.mjs", "default": "./dist/changesets-should-skip-package.cjs.js"}, "./package.json": "./package.json"}, "license": "MIT", "repository": "https://github.com/changesets/changesets/tree/main/packages/should-skip-package", "dependencies": {"@changesets/types": "^6.1.0", "@manypkg/get-packages": "^1.1.3"}, "devDependencies": {"@changesets/test-utils": "*"}}