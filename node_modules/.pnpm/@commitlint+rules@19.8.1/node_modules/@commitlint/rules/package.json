{"name": "@commitlint/rules", "type": "module", "version": "19.8.1", "description": "Lint your commit messages", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/"], "scripts": {"deps": "dep-check", "pkg": "pkg-check"}, "engines": {"node": ">=v18"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/rules"}, "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "keywords": ["conventional-changelog", "commitlint", "library", "core"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/parse": "^19.8.1", "@commitlint/test": "^19.8.1", "@commitlint/utils": "^19.8.1", "conventional-changelog-angular": "^7.0.0", "glob": "^10.3.10"}, "dependencies": {"@commitlint/ensure": "^19.8.1", "@commitlint/message": "^19.8.1", "@commitlint/to-lines": "^19.8.1", "@commitlint/types": "^19.8.1"}, "gitHead": "3c302008cabeb0b08cd246b2417a51a9d745a918"}