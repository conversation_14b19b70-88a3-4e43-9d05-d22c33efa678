{"name": "outdent", "version": "0.5.0", "description": "Remove leading indentation from ES6 template literals.", "main": "lib/index.js", "jsnext:main": "lib-module/index.js", "module": "lib-module/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "ts-node -F -P ./scripts/tsconfig.json ./scripts/npm-run.ts", "build": "ts-node -F -P ./scripts/tsconfig.json ./scripts/npm-run.ts", "test": "ts-node -F -P ./scripts/tsconfig.json ./scripts/npm-run.ts", "lint": "ts-node -F -P ./scripts/tsconfig.json ./scripts/npm-run.ts", "format": "ts-node -F -P ./scripts/tsconfig.json ./scripts/npm-run.ts", "prepack": "ts-node -F -P ./scripts/tsconfig.json ./scripts/npm-run.ts", "setup": "ts-node -F -P ./scripts/tsconfig.json ./scripts/npm-run.ts"}, "repository": {"type": "git", "url": "git+https://github.com/cspotcode/outdent.git"}, "keywords": ["es6", "es2015", "template string", "template literal", "interpolation", "string", "template", "indent"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/cspotcode/outdent/issues"}, "homepage": "https://github.com/cspotcode/outdent#readme", "devDependencies": {"@types/chai": "^4.0.4", "@types/mocha": "^2.2.43", "@types/node": "^8.0.44", "@types/rimraf": "^2.0.2", "@types/source-map-support": "^0.4.0", "@types/which": "^1.0.28", "chai": "^4.1.2", "mocha": "^4.0.1", "rimraf": "^2.6.2", "source-map-support": "^0.5.0", "ts-node": "^3.3.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.6", "typescript": "^2.7.2", "typescript-formatter": "^6.0.0", "which": "^1.3.0"}, "files": ["lib", "lib-module", "src", "LICENSE", "README.md", "tsconfig-module.json", "tsconfig.json"], "dependencies": {}}