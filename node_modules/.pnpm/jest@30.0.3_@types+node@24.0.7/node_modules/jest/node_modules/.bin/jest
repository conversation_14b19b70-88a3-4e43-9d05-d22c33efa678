#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/jest@30.0.3_@types+node@24.0.7/node_modules/jest/bin/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/jest@30.0.3_@types+node@24.0.7/node_modules/jest/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/jest@30.0.3_@types+node@24.0.7/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/jest@30.0.3_@types+node@24.0.7/node_modules/jest/bin/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/jest@30.0.3_@types+node@24.0.7/node_modules/jest/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/jest@30.0.3_@types+node@24.0.7/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/jest.js" "$@"
else
  exec node  "$basedir/../../bin/jest.js" "$@"
fi
