{"name": "mri", "version": "1.2.0", "description": "Quickly scan for CLI flags and arguments", "repository": "lukeed/mri", "module": "lib/index.mjs", "main": "lib/index.js", "types": "index.d.ts", "license": "MIT", "files": ["*.d.ts", "lib"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=4"}, "scripts": {"build": "bundt", "bench": "node bench", "pretest": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["argv", "arguments", "cli", "minimist", "options", "optimist", "parser", "args"], "devDependencies": {"bundt": "1.0.2", "tap-spec": "4.1.2", "tape": "4.13.3"}}