{"name": "git-raw-commits", "version": "4.0.0", "description": "Get raw git commits out of your repository using git-log(1)", "bugs": {"url": "https://github.com/conventional-changelog/conventional-changelog/issues"}, "homepage": "https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/git-raw-commits#readme", "author": {"name": "<PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com", "url": "https://github.com/stevemao"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/conventional-changelog.git"}, "license": "MIT", "engines": {"node": ">=16"}, "files": ["index.js", "cli.mjs"], "keywords": ["git-raw-commits", "raw", "commit", "commits", "git", "log", "git-log"], "dependencies": {"dargs": "^8.0.0", "meow": "^12.0.1", "split2": "^4.0.0"}, "bin": {"git-raw-commits": "cli.mjs"}}