{"name": "cosmiconfig-typescript-loader", "version": "6.1.0", "description": "TypeScript loader for cosmiconfig", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/Codex-/cosmiconfig-typescript-loader#readme", "repository": {"type": "git", "url": "git+https://github.com/Codex-/cosmiconfig-typescript-loader.git"}, "bugs": {"url": "https://github.com/Codex-/cosmiconfig-typescript-loader/issues"}, "files": ["dist/**/*"], "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "types": "dist/types/index.d.ts", "exports": {".": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.cjs"}}}, "scripts": {"build": "pnpm build:types && pnpm build:sources", "build:sources": "node ./scripts/esbuild.config.mjs", "build:types": "tsc -p tsconfig.build.json", "check:types": "tsc -p tsconfig.json", "format": "pnpm format:check --write", "format:check": "prettier --check \"{**/*,*}.{js,cjs,mjs,ts}\"", "lint": "eslint", "lint:fix": "pnpm lint --fix", "release": "release-it", "test": "jest", "test:coverage": "jest --coverage"}, "engines": {"node": ">=v18"}, "peerDependencies": {"@types/node": "*", "cosmiconfig": ">=9", "typescript": ">=5"}, "dependencies": {"jiti": "^2.4.1"}, "devDependencies": {"@swc/core": "^1.10.0", "@swc/jest": "^0.2.37", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^8.17.0", "auto-changelog": "^2.5.0", "chalk": "^5.3.0", "cosmiconfig": "^9.0.0", "esbuild": "^0.24.0", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import-x": "^4.5.0", "jest": "^29.7.0", "prettier": "^3.4.2", "release-it": "^17.10.0", "typescript": "^5.7.2", "typescript-eslint": "^8.17.0"}, "keywords": ["cosmiconfig", "typescript"]}