{"name": "compare-func", "version": "2.0.0", "description": "Get a compare function for array to sort", "homepage": "https://github.com/stevemao/compare-func", "author": {"name": "<PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com", "url": "https://github.com/stevemao"}, "repository": "stevemao/compare-func", "license": "MIT", "files": ["index.js"], "keywords": ["compare-func", "arr", "array", "by", "compare", "dot", "get", "obj", "object", "prop", "property", "sort", "sorting"], "dependencies": {"array-ify": "^1.0.0", "dot-prop": "^5.1.0"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.4.4", "jscs": "^3.0.5", "jshint": "^2.7.0", "mocha": "^7.1.2"}, "scripts": {"coverage": "istanbul cover _mocha -- -R spec && rm -rf ./coverage", "lint": "jshint *.js --exclude node_modules && jscs *.js", "test": "npm run-script lint && mocha"}}