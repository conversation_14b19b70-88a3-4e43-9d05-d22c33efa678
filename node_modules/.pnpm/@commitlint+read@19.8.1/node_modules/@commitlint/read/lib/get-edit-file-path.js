import path from "node:path";
import fs from "fs/promises";
// Get path to recently edited commit message file
export async function getEditFilePath(top, edit) {
    if (typeof edit === "string") {
        return path.resolve(top, edit);
    }
    const dotgitPath = path.join(top, ".git");
    const dotgitStats = await fs.lstat(dotgitPath);
    if (dotgitStats.isDirectory()) {
        return path.join(top, ".git/COMMIT_EDITMSG");
    }
    const gitFile = await fs.readFile(dotgitPath, {
        encoding: "utf-8",
    });
    const relativeGitPath = gitFile.replace("gitdir: ", "").replace("\n", "");
    return path.resolve(top, relativeGitPath, "COMMIT_EDITMSG");
}
//# sourceMappingURL=get-edit-file-path.js.map