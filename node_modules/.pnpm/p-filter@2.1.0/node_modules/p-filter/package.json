{"name": "p-filter", "version": "2.1.0", "description": "Filter promises concurrently", "license": "MIT", "repository": "sindresorhus/p-filter", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "filter", "collection", "iterable", "iterator", "fulfilled", "async", "await", "promises", "concurrently", "concurrency", "parallel", "bluebird"], "dependencies": {"p-map": "^2.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}