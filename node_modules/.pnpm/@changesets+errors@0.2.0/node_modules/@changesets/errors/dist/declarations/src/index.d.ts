import ExtendableError from "extendable-error";
export declare class GitError extends ExtendableError {
    code: number;
    constructor(code: number, message: string);
}
export declare class ValidationError extends ExtendableError {
}
export declare class ExitError extends ExtendableError {
    code: number;
    constructor(code: number);
}
export declare class PreExitButNotInPreModeError extends ExtendableError {
    constructor();
}
export declare class PreEnterButInPreModeError extends ExtendableError {
    constructor();
}
export declare class InternalError extends ExtendableError {
    constructor(message: string);
}
