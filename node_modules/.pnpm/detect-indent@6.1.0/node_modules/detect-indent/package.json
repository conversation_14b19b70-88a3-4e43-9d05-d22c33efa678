{"name": "detect-indent", "version": "6.1.0", "description": "Detect the indentation of code", "license": "MIT", "repository": "sindresorhus/detect-indent", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["indent", "indentation", "detect", "infer", "identify", "code", "string", "text", "source", "space", "tab"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "xo": {"ignores": ["fixture"]}}