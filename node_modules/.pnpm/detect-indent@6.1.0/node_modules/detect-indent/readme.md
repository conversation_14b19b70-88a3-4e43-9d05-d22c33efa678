# detect-indent

> Detect the indentation of code

Pass in a string of any kind of text and get the indentation.


## Use cases

- Persisting the indentation when modifying a file.
- Have new content match the existing indentation.
- Setting the right indentation in your editor.


## Install

```
$ npm install detect-indent
```


## Usage

Here we modify a JSON file while persisting the indentation:

```js
const fs = require('fs');
const detectIndent = require('detect-indent');

/*
{
    "ilove": "pizza"
}
*/
const file = fs.readFileSync('foo.json', 'utf8');

// Tries to detect the indentation and falls back to a default if it can't
const indent = detectIndent(file).indent || '    ';

const json = JSON.parse(file);

json.ilove = 'unicorns';

fs.writeFileSync('foo.json', JSON.stringify(json, null, indent));
/*
{
    "ilove": "unicorns"
}
*/
```


## API

Accepts a string and returns an object with stats about the indentation:

* `amount` {number} - Amount of indentation, for example `2`
* `type` {'tab' | 'space' | undefined} - Type of indentation. Possible values are `'tab'`, `'space'` or `undefined` if no indentation is detected
* `indent` {string} - Actual indentation


## Algorithm

The current algorithm looks for the most common difference between two consecutive non-empty lines.

In the following example, even if the 4-space indentation is used 3 times whereas the 2-space one is used 2 times, it is detected as less used because there were only 2 differences with this value instead of 4 for the 2-space indentation:

```css
html {
  box-sizing: border-box;
}

body {
  background: gray;
}

p {
    line-height: 1.3em;
    margin-top: 1em;
    text-indent: 2em;
}
```

[Source.](https://medium.com/@heatherarthur/detecting-code-indentation-eff3ed0fb56b#3918)

Furthermore, if there are more than one most used difference, the indentation with the most lines is selected.

In the following example, the indentation is detected as 4-spaces:

```css
body {
  background: gray;
}

p {
    line-height: 1.3em;
    margin-top: 1em;
    text-indent: 2em;
}
```


## Related

- [detect-indent-cli](https://github.com/sindresorhus/detect-indent-cli) - CLI for this module
- [detect-newline](https://github.com/sindresorhus/detect-newline) - Detect the dominant newline character of a string
- [detect-indent-rs](https://github.com/stefanpenner/detect-indent-rs) - Rust port


---

<div align="center">
  <b>
    <a href="https://tidelift.com/subscription/pkg/npm-detect-indent?utm_source=npm-detect-indent&utm_medium=referral&utm_campaign=readme">Get professional support for this package with a Tidelift subscription</a>
  </b>
  <br>
  <sub>
    Tidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.
  </sub>
</div>
