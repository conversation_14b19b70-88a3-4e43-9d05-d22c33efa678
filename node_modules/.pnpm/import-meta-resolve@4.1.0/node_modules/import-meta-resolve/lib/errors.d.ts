export namespace codes {
    let ERR_INVALID_ARG_TYPE: new (...parameters: any[]) => Error;
    let ERR_INVALID_MODULE_SPECIFIER: new (...parameters: any[]) => Error;
    let ERR_INVALID_PACKAGE_CONFIG: new (...parameters: any[]) => Error;
    let ERR_INVALID_PACKAGE_TARGET: new (...parameters: any[]) => Error;
    let ERR_MODULE_NOT_FOUND: new (...parameters: any[]) => Error;
    let ERR_NETWORK_IMPORT_DISALLOWED: new (...parameters: any[]) => Error;
    let ERR_PACKAGE_IMPORT_NOT_DEFINED: new (...parameters: any[]) => Error;
    let ERR_PACKAGE_PATH_NOT_EXPORTED: new (...parameters: any[]) => Error;
    let ERR_UNSUPPORTED_DIR_IMPORT: new (...parameters: any[]) => Error;
    let ERR_UNSUPPORTED_RESOLVE_REQUEST: new (...parameters: any[]) => Error;
    let ERR_UNKNOWN_FILE_EXTENSION: new (...parameters: any[]) => Error;
    let ERR_INVALID_ARG_VALUE: new (...parameters: any[]) => Error;
}
export type ErrnoExceptionFields = {
    errnode?: number | undefined;
    code?: string | undefined;
    path?: string | undefined;
    syscall?: string | undefined;
    url?: string | undefined;
};
export type ErrnoException = Error & ErrnoExceptionFields;
export type MessageFunction = (...parameters: Array<any>) => string;
