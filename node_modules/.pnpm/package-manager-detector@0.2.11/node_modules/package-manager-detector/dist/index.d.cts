export { COMMANDS, constructCommand, resolveCommand } from './commands.cjs';
export { AGENTS, INSTALL_PAGE, LOCKS } from './constants.cjs';
export { detect, detectSync, getUserAgent } from './detect.cjs';
export { A as Agent, b as AgentCommandValue, c as AgentCommands, a as AgentName, C as Command, D as DetectOptions, d as DetectResult, R as ResolvedCommand } from './shared/package-manager-detector.ncFwAKgD.cjs';
import 'quansync/types';
