# @changesets/get-release-plan

## 4.0.13

### Patch Changes

- Updated dependencies [[`6352819`](https://github.com/changesets/changesets/commit/6352819685369daecf31f72e948d0fe92ff33485)]:
  - @changesets/assemble-release-plan@6.0.9

## 4.0.12

### Patch Changes

- Updated dependencies [[`65d6632`](https://github.com/changesets/changesets/commit/65d663278867b0495d49e9e3e9c5c4c0158b8627)]:
  - @changesets/assemble-release-plan@6.0.8

## 4.0.11

### Patch Changes

- Updated dependencies [[`de8bebc`](https://github.com/changesets/changesets/commit/de8bebc93b81cb333c3c7e1ed8a3687926b7fcd8)]:
  - @changesets/assemble-release-plan@6.0.7

## 4.0.10

### Patch Changes

- Updated dependencies [[`f73f84a`](https://github.com/changesets/changesets/commit/f73f84ac2d84d3ccf5ff55c0fc78aaaf3f3da20d)]:
  - @changesets/read@0.6.5

## 4.0.9

### Patch Changes

- Updated dependencies []:
  - @changesets/read@0.6.4

## 4.0.8

### Patch Changes

- Updated dependencies [[`b9df596`](https://github.com/changesets/changesets/commit/b9df59627623bcf211ddbdab4df7cc07028d4998)]:
  - @changesets/config@3.1.1

## 4.0.7

### Patch Changes

- Updated dependencies [[`84a4a1b`](https://github.com/changesets/changesets/commit/84a4a1b1d399bfd0a58677b0182b9c053194febf)]:
  - @changesets/types@6.1.0
  - @changesets/config@3.1.0
  - @changesets/assemble-release-plan@6.0.6
  - @changesets/pre@2.0.2
  - @changesets/read@0.6.3

## 4.0.6

### Patch Changes

- Updated dependencies [[`f0270f6`](https://github.com/changesets/changesets/commit/f0270f69793ddb6865f2510d334864b093cb10e3)]:
  - @changesets/config@3.0.5

## 4.0.5

### Patch Changes

- Updated dependencies [[`7323704`](https://github.com/changesets/changesets/commit/7323704dff6e76f488370db384579b86c95c866f), [`26c8ba9`](https://github.com/changesets/changesets/commit/26c8ba9f58d3b123a19106771b4bdcb2800235cc), [`82cacb2`](https://github.com/changesets/changesets/commit/82cacb2227cf3a215cd9d29b9fb0c860f20ba2ca)]:
  - @changesets/config@3.0.4
  - @changesets/assemble-release-plan@6.0.5
  - @changesets/read@0.6.2

## 4.0.4

### Patch Changes

- [#1445](https://github.com/changesets/changesets/pull/1445) [`52c302a`](https://github.com/changesets/changesets/commit/52c302a48a662f71585f18f91dad3cbe49d75890) Thanks [@bluwy](https://github.com/bluwy)! - Remove unused `@babel/runtime` dependency

- Updated dependencies [[`bc75c1a`](https://github.com/changesets/changesets/commit/bc75c1a74c2d46e08620c7aa0e9f4f5ef40a9b55), [`52c302a`](https://github.com/changesets/changesets/commit/52c302a48a662f71585f18f91dad3cbe49d75890)]:
  - @changesets/read@0.6.1
  - @changesets/assemble-release-plan@6.0.4
  - @changesets/pre@2.0.1
  - @changesets/config@3.0.3

## 4.0.3

### Patch Changes

- Updated dependencies [[`dd6e5bb`](https://github.com/changesets/changesets/commit/dd6e5bbf74e246d7a742aa50424989462679b0ca)]:
  - @changesets/config@3.0.2
  - @changesets/assemble-release-plan@6.0.3

## 4.0.2

### Patch Changes

- Updated dependencies []:
  - @changesets/assemble-release-plan@6.0.2
  - @changesets/config@3.0.1

## 4.0.1

### Patch Changes

- Updated dependencies [[`954a16a`](https://github.com/changesets/changesets/commit/954a16aa1d118a0f7fa745ffe0d19b304f685d4c)]:
  - @changesets/assemble-release-plan@6.0.1

## 4.0.0

### Major Changes

- [#1185](https://github.com/changesets/changesets/pull/1185) [`a971652`](https://github.com/changesets/changesets/commit/a971652ec1403aab3fb89eb2f1640bd5012b895a) Thanks [@Andarist](https://github.com/Andarist)! - `package.json#exports` have been added to limit what (and how) code might be imported from the package.

### Patch Changes

- Updated dependencies [[`a971652`](https://github.com/changesets/changesets/commit/a971652ec1403aab3fb89eb2f1640bd5012b895a)]:
  - @changesets/assemble-release-plan@6.0.0
  - @changesets/config@3.0.0
  - @changesets/types@6.0.0
  - @changesets/read@0.6.0
  - @changesets/pre@2.0.0

## 3.0.17

### Patch Changes

- Updated dependencies [[`41988ce`](https://github.com/changesets/changesets/commit/41988ceb8c1cedd3857c939448bf3965494ff0a4)]:
  - @changesets/assemble-release-plan@5.2.4
  - @changesets/config@2.3.1

## 3.0.16

### Patch Changes

- Updated dependencies [[`521205d`](https://github.com/changesets/changesets/commit/521205dc8c70fe71b181bd3c4bb7c9c6d2e721d2), [`521205d`](https://github.com/changesets/changesets/commit/521205dc8c70fe71b181bd3c4bb7c9c6d2e721d2)]:
  - @changesets/config@2.3.0
  - @changesets/types@5.2.1
  - @changesets/read@0.5.9
  - @changesets/assemble-release-plan@5.2.3
  - @changesets/pre@1.0.14

## 3.0.15

### Patch Changes

- Updated dependencies [[`8c08469`](https://github.com/changesets/changesets/commit/8c0846977597ddaf51aaeb35f1f0f9428bf8ba14), [`64585ea`](https://github.com/changesets/changesets/commit/64585ea4323c4cf51a23b0635990b568d1f58b2b)]:
  - @changesets/config@2.2.0
  - @changesets/types@5.2.0
  - @changesets/assemble-release-plan@5.2.2
  - @changesets/read@0.5.8
  - @changesets/pre@1.0.13

## 3.0.14

### Patch Changes

- Updated dependencies [[`b023e4b`](https://github.com/changesets/changesets/commit/b023e4b3d1ad793a5dd1187b720e8103cebfb937)]:
  - @changesets/assemble-release-plan@5.2.1

## 3.0.13

### Patch Changes

- Updated dependencies [[`7d998ee`](https://github.com/changesets/changesets/commit/7d998eeb16064b5442ebc49ad31dec7b841d504e)]:
  - @changesets/config@2.1.1

## 3.0.12

### Patch Changes

- Updated dependencies []:
  - @changesets/read@0.5.7

## 3.0.11

### Patch Changes

- Updated dependencies [[`dd9b76f`](https://github.com/changesets/changesets/commit/dd9b76f162a546ae8b412e0cb10277f971f3585e), [`dd9b76f`](https://github.com/changesets/changesets/commit/dd9b76f162a546ae8b412e0cb10277f971f3585e)]:
  - @changesets/config@2.1.0
  - @changesets/assemble-release-plan@5.2.0
  - @changesets/types@5.1.0
  - @changesets/read@0.5.6
  - @changesets/pre@1.0.12

## 3.0.10

### Patch Changes

- Updated dependencies [[`2827c7a`](https://github.com/changesets/changesets/commit/2827c7ab33af30065fafe72ede1a2a6ac88d5276), [`7b1c0c1`](https://github.com/changesets/changesets/commit/7b1c0c1b73a19b50fe3a104acb440c604eab108f)]:
  - @changesets/config@2.0.1

## 3.0.9

### Patch Changes

- Updated dependencies [[`d6bfcc5`](https://github.com/changesets/changesets/commit/d6bfcc5052dcba18c521a20d62e2e67a81819112)]:
  - @changesets/assemble-release-plan@5.1.3

## 3.0.8

### Patch Changes

- Updated dependencies [[`c87eba6`](https://github.com/changesets/changesets/commit/c87eba6f80a34563b7382f87472c29f6dafb546c), [`c87eba6`](https://github.com/changesets/changesets/commit/c87eba6f80a34563b7382f87472c29f6dafb546c)]:
  - @changesets/types@5.0.0
  - @changesets/config@2.0.0
  - @changesets/assemble-release-plan@5.1.2
  - @changesets/pre@1.0.11
  - @changesets/read@0.5.5

## 3.0.7

### Patch Changes

- Updated dependencies [[`3e8e672`](https://github.com/changesets/changesets/commit/3e8e6721d31f80fff28826e0fad2c14216c3d94f)]:
  - @changesets/assemble-release-plan@5.1.1

## 3.0.6

### Patch Changes

- Updated dependencies [[`0812858`](https://github.com/changesets/changesets/commit/0812858996045e602d22f2b7dd13e8673e3b36b0), [`27a5a82`](https://github.com/changesets/changesets/commit/27a5a82188914570d192162f9d045dfd082a3c15), [`27a5a82`](https://github.com/changesets/changesets/commit/27a5a82188914570d192162f9d045dfd082a3c15), [`27a5a82`](https://github.com/changesets/changesets/commit/27a5a82188914570d192162f9d045dfd082a3c15), [`59c7ebc`](https://github.com/changesets/changesets/commit/59c7ebc7a5e75f69f5487e95a85cd1b7062ac39d), [`15c461d`](https://github.com/changesets/changesets/commit/15c461d5de94a274ccc8b33755a133a513339b0a)]:
  - @changesets/assemble-release-plan@5.1.0
  - @changesets/types@4.1.0
  - @changesets/config@1.7.0
  - @changesets/pre@1.0.10
  - @changesets/read@0.5.4

## 3.0.5

### Patch Changes

- Updated dependencies [[`1be201f`](https://github.com/changesets/changesets/commit/1be201fc27903cb2f42137400b5484c4ccad3812)]:
  - @changesets/assemble-release-plan@5.0.5
  - @changesets/config@1.6.4
  - @changesets/read@0.5.3

## 3.0.4

### Patch Changes

- Updated dependencies [[`82be80e`](https://github.com/changesets/changesets/commit/82be80ecfe9288535071e850ae56f2e7a7006eba), [`e4c4b29`](https://github.com/changesets/changesets/commit/e4c4b2934beee9b25a927b4ae1b7280f4323d4ff)]:
  - @changesets/pre@1.0.9
  - @changesets/assemble-release-plan@5.0.4

## 3.0.3

### Patch Changes

- [#667](https://github.com/changesets/changesets/pull/667) [`fe8db75`](https://github.com/changesets/changesets/commit/fe8db7500f81caea9064f8bec02bcb77e0fd8fce) Thanks [@fz6m](https://github.com/fz6m)! - Upgraded `@manypkg/get-packages` dependency to fix getting correct packages in pnpm workspaces with exclude rules.

- Updated dependencies [[`fe8db75`](https://github.com/changesets/changesets/commit/fe8db7500f81caea9064f8bec02bcb77e0fd8fce), [`9a993ba`](https://github.com/changesets/changesets/commit/9a993ba09629c1620d749432520470cec49d3a96)]:
  - @changesets/assemble-release-plan@5.0.3
  - @changesets/config@1.6.3
  - @changesets/pre@1.0.8
  - @changesets/types@4.0.2
  - @changesets/read@0.5.2

## 3.0.2

### Patch Changes

- Updated dependencies []:
  - @changesets/read@0.5.1
  - @changesets/assemble-release-plan@5.0.2
  - @changesets/config@1.6.2

## 3.0.1

### Patch Changes

- Updated dependencies [[`bc611cf`](https://github.com/changesets/changesets/commit/bc611cf2104ff8170e9ea8acb10952ea8cc2a784), [`e89e28a`](https://github.com/changesets/changesets/commit/e89e28a05f5fa43307db73812a6bcd269b62ddee)]:
  - @changesets/read@0.5.0
  - @changesets/types@4.0.1
  - @changesets/assemble-release-plan@5.0.1
  - @changesets/config@1.6.1
  - @changesets/pre@1.0.7

## 3.0.0

### Major Changes

- [#542](https://github.com/changesets/changesets/pull/542) [`de2b4a5`](https://github.com/changesets/changesets/commit/de2b4a5a7b244a37d94625bcb70ecde9dde5b612) Thanks [@Andarist](https://github.com/Andarist)! - The accepted `Config` type has been changed - a new experimental option (`updateInternalDependents`) was added to it.

### Patch Changes

- Updated dependencies [[`de2b4a5`](https://github.com/changesets/changesets/commit/de2b4a5a7b244a37d94625bcb70ecde9dde5b612), [`de2b4a5`](https://github.com/changesets/changesets/commit/de2b4a5a7b244a37d94625bcb70ecde9dde5b612)]:
  - @changesets/config@1.6.0
  - @changesets/types@4.0.0
  - @changesets/assemble-release-plan@5.0.0
  - @changesets/pre@1.0.6
  - @changesets/read@0.4.7

## 2.0.1

### Patch Changes

- Updated dependencies [[`ab98fe3`](https://github.com/changesets/changesets/commit/ab98fe33814867ba740fc04733602be80771915c), [`d1d987c`](https://github.com/changesets/changesets/commit/d1d987c42cddff8be5d7f04d3ebb5a262779fa9f), [`9d99bd1`](https://github.com/changesets/changesets/commit/9d99bd16f2b6b3ab4fe820358d4c9f313cb2ae76)]:
  - @changesets/assemble-release-plan@4.0.0

## 2.0.0

### Major Changes

- [`addd725`](https://github.com/changesets/changesets/commit/addd7256d9251d999251a7c16c0a0b068d557b5d) [#383](https://github.com/changesets/changesets/pull/383) Thanks [@Feiyang1](https://github.com/Feiyang1)! - Added an experimental flag `onlyUpdatePeerDependentsWhenOutOfRange`. When set to `true`, we only bump peer dependents when peerDependencies are leaving range.

### Patch Changes

- Updated dependencies [[`00e768e`](https://github.com/changesets/changesets/commit/00e768e4af921a894debb900f944d4c9a4e27997), [`addd725`](https://github.com/changesets/changesets/commit/addd7256d9251d999251a7c16c0a0b068d557b5d), [`9dcc364`](https://github.com/changesets/changesets/commit/9dcc364bf19e48f8f2824ebaf967d9ef41b6fc04), [`9dcc364`](https://github.com/changesets/changesets/commit/9dcc364bf19e48f8f2824ebaf967d9ef41b6fc04)]:
  - @changesets/assemble-release-plan@3.0.0
  - @changesets/config@1.2.0
  - @changesets/types@3.1.0

## 1.0.4

### Patch Changes

- Updated dependencies [[`2b49d66`](https://github.com/changesets/changesets/commit/2b49d668ecaa1333bc5c7c5be4648dda1b11528d)]:
  - @changesets/types@3.0.0
  - @changesets/config@1.1.0
  - @changesets/assemble-release-plan@2.0.4
  - @changesets/pre@1.0.4
  - @changesets/read@0.4.6

## 1.0.3

### Patch Changes

- [`1706fb7`](https://github.com/changesets/changesets/commit/1706fb751ecc2f5a792c42f467b2063078d58716) [#321](https://github.com/changesets/changesets/pull/321) Thanks [@mitchellhamilton](https://github.com/mitchellhamilton)! - Fix TypeScript declarations

- Updated dependencies [[`1706fb7`](https://github.com/changesets/changesets/commit/1706fb751ecc2f5a792c42f467b2063078d58716)]:
  - @changesets/assemble-release-plan@2.0.1
  - @changesets/config@1.0.3
  - @changesets/pre@1.0.3
  - @changesets/read@0.4.5
  - @changesets/types@2.0.1

## 1.0.2

### Patch Changes

- Updated dependencies [[`011d57f`](https://github.com/changesets/changesets/commit/011d57f1edf9e37f75a8bef4f918e72166af096e), [`011d57f`](https://github.com/changesets/changesets/commit/011d57f1edf9e37f75a8bef4f918e72166af096e)]:
  - @changesets/assemble-release-plan@2.0.0
  - @changesets/types@2.0.0
  - @changesets/config@1.0.2
  - @changesets/pre@1.0.2
  - @changesets/read@0.4.4

## 1.0.1

### Patch Changes

- [`04ddfd7`](https://github.com/changesets/changesets/commit/04ddfd7c3acbfb84ef9c92873fe7f9dea1f5145c) [#305](https://github.com/changesets/changesets/pull/305) Thanks [@Noviny](https://github.com/Noviny)! - Add link to changelog in readme

- [`b49e1cf`](https://github.com/changesets/changesets/commit/b49e1cff65dca7fe9e341a35aa91704aa0e51cb3) [#306](https://github.com/changesets/changesets/pull/306) Thanks [@Andarist](https://github.com/Andarist)! - Ignore `node_modules` when glob searching for packages. This fixes an issue with package cycles.

- Updated dependencies [[`04ddfd7`](https://github.com/changesets/changesets/commit/04ddfd7c3acbfb84ef9c92873fe7f9dea1f5145c), [`e56928b`](https://github.com/changesets/changesets/commit/e56928bbd6f9096def06ac37487bdbf28efec9d1), [`b49e1cf`](https://github.com/changesets/changesets/commit/b49e1cff65dca7fe9e341a35aa91704aa0e51cb3)]:
  - @changesets/assemble-release-plan@1.0.1
  - @changesets/config@1.0.1
  - @changesets/pre@1.0.1
  - @changesets/read@0.4.3
  - @changesets/types@1.0.1

## 1.0.0

### Major Changes

- [`cc8c921`](https://github.com/changesets/changesets/commit/cc8c92143d4c4b7cca8b9917dfc830a40b5cda20) [#290](https://github.com/changesets/changesets/pull/290) Thanks [@mitchellhamilton](https://github.com/mitchellhamilton)! - Use `@manypkg/get-packages` instead of `get-workspaces` internally

### Patch Changes

- Updated dependencies [[`41e2e3d`](https://github.com/changesets/changesets/commit/41e2e3dd1053ff2f35a1a07e60793c9099f26997), [`cc8c921`](https://github.com/changesets/changesets/commit/cc8c92143d4c4b7cca8b9917dfc830a40b5cda20), [`cc8c921`](https://github.com/changesets/changesets/commit/cc8c92143d4c4b7cca8b9917dfc830a40b5cda20), [`cc8c921`](https://github.com/changesets/changesets/commit/cc8c92143d4c4b7cca8b9917dfc830a40b5cda20), [`2363366`](https://github.com/changesets/changesets/commit/2363366756d1b15bddf6d803911baccfca03cbdf), [`cc8c921`](https://github.com/changesets/changesets/commit/cc8c92143d4c4b7cca8b9917dfc830a40b5cda20), [`cc8c921`](https://github.com/changesets/changesets/commit/cc8c92143d4c4b7cca8b9917dfc830a40b5cda20)]:
  - @changesets/types@1.0.0
  - @changesets/pre@1.0.0
  - @changesets/config@1.0.0
  - @changesets/assemble-release-plan@1.0.0
  - @changesets/read@0.4.2

## 0.4.0

### Minor Changes

- [`fe0d9192`](https://github.com/changesets/changesets/commit/fe0d9192544646e1a755202b87dfe850c1c200a3) [#236](https://github.com/changesets/changesets/pull/236) Thanks [@Andarist](https://github.com/Andarist)! - Read also pnpm workspace packages when searching for packages.

### Patch Changes

- Updated dependencies [[`fe0d9192`](https://github.com/changesets/changesets/commit/fe0d9192544646e1a755202b87dfe850c1c200a3), [`fe0d9192`](https://github.com/changesets/changesets/commit/fe0d9192544646e1a755202b87dfe850c1c200a3)]:
  - get-workspaces@0.6.0
  - get-dependents-graph@0.2.0
  - @changesets/pre@0.2.0
  - @changesets/read@0.4.1

## 0.3.0

### Minor Changes

- [`bca8865`](https://github.com/changesets/changesets/commit/bca88652d38caa31e789c4564230ba0b49562ad2) [#221](https://github.com/changesets/changesets/pull/221) Thanks [@mitchellhamilton](https://github.com/mitchellhamilton)! - Added support for reading old changesets from v1

- [`bca8865`](https://github.com/changesets/changesets/commit/bca88652d38caa31e789c4564230ba0b49562ad2) [#221](https://github.com/changesets/changesets/pull/221) Thanks [@mitchellhamilton](https://github.com/mitchellhamilton)! - Replaced sinceMaster parameter with sinceRef parameter which can be any git ref such as a branch, tag, commit or etc.

### Patch Changes

- Updated dependencies [[`bca8865`](https://github.com/changesets/changesets/commit/bca88652d38caa31e789c4564230ba0b49562ad2), [`bca8865`](https://github.com/changesets/changesets/commit/bca88652d38caa31e789c4564230ba0b49562ad2), [`bca8865`](https://github.com/changesets/changesets/commit/bca88652d38caa31e789c4564230ba0b49562ad2)]:
  - @changesets/read@0.4.0
  - @changesets/config@0.3.0

## 0.2.0

### Minor Changes

- [`8f0a1ef`](https://github.com/changesets/changesets/commit/8f0a1ef327563512f471677ef0ca99d30da009c0) [#183](https://github.com/changesets/changesets/pull/183) Thanks [@mitchellhamilton](https://github.com/mitchellhamilton)! - Add support for prereleases. For more information, see [the docs on prereleases](https://github.com/changesets/changesets/blob/main/docs/prereleases.md).

### Patch Changes

- Updated dependencies [[`8f0a1ef`](https://github.com/changesets/changesets/commit/8f0a1ef327563512f471677ef0ca99d30da009c0), [`8f0a1ef`](https://github.com/changesets/changesets/commit/8f0a1ef327563512f471677ef0ca99d30da009c0), [`8f0a1ef`](https://github.com/changesets/changesets/commit/8f0a1ef327563512f471677ef0ca99d30da009c0), [`8f0a1ef`](https://github.com/changesets/changesets/commit/8f0a1ef327563512f471677ef0ca99d30da009c0)]:
  - @changesets/assemble-release-plan@0.3.0
  - @changesets/types@0.4.0
  - @changesets/pre@0.1.0
  - @changesets/config@0.2.3
  - get-dependents-graph@0.1.2
  - get-workspaces@0.5.2
  - @changesets/read@0.3.1

## 0.1.3

### Patch Changes

- Updated dependencies [[`8dce96f`](https://github.com/changesets/changesets/commit/8dce96f8aec43f82b35e65f54b06cbeed3275885)]:
  - @changesets/read@0.3.0

## 0.1.2

### Patch Changes

- [89c0894](https://github.com/changesets/changesets/commit/89c08944fac84f71241305e359e9717ad4ec1b62) [#167](https://github.com/changesets/changesets/pull/167) Thanks [@Noviny](https://github.com/Noviny)! - Fix broken `sinceMaster` arg - which was not working with v2 changesets

- Updated dependencies [89c0894]:
  - @changesets/read@0.2.2

## 0.1.1

### Patch Changes

- [3dd003c](https://github.com/changesets/changesets/commit/3dd003cb0ccadafa083c91f10cf257ee869df042) [#166](https://github.com/changesets/changesets/pull/166) Thanks [@Noviny](https://github.com/Noviny)! - Make passedConfig an optional variable to ease frustration

- [8c43fa0](https://github.com/changesets/changesets/commit/8c43fa061e2a5a01e4f32504ed351d261761c8dc) [#155](https://github.com/changesets/changesets/pull/155) Thanks [@Noviny](https://github.com/Noviny)! - Add Readme

- Updated dependencies [8c43fa0, 0320391, 1ff73b7]:
  - @changesets/assemble-release-plan@0.2.1
  - get-dependents-graph@0.1.1
  - @changesets/read@0.2.1
  - @changesets/types@0.3.0
  - @changesets/config@0.2.1

## 0.1.0

### Minor Changes

- [ca8ff585](https://github.com/changesets/changesets/commit/ca8ff585) [#147](https://github.com/changesets/changesets/pull/147) Thanks [@Noviny](https://github.com/Noviny)! - Initial Release

- [296a6731](https://github.com/changesets/changesets/commit/296a6731) - Safety bump: Towards the end of preparing changesets v2, there was a lot of chaos - this bump is to ensure every package on npm matches what is found in the repository.

### Patch Changes

- Updated dependencies [296a6731]:
  - @changesets/assemble-release-plan@0.2.0
  - @changesets/config@0.2.0
  - get-dependents-graph@0.1.0
  - get-workspaces@0.5.0
  - @changesets/read@0.2.0
  - @changesets/types@0.2.0
