#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/ejs@3.1.10/node_modules/ejs/bin/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/ejs@3.1.10/node_modules/ejs/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/ejs@3.1.10/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/ejs@3.1.10/node_modules/ejs/bin/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/ejs@3.1.10/node_modules/ejs/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/ejs@3.1.10/node_modules:/Users/<USER>/projects/mcp-server-framework/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../ejs@3.1.10/node_modules/ejs/bin/cli.js" "$@"
else
  exec node  "$basedir/../../../../../ejs@3.1.10/node_modules/ejs/bin/cli.js" "$@"
fi
