import cjsModule from './index.js';

export const EXTENSION = cjsModule.EXTENSION;
export const SnapshotState = cjsModule.SnapshotState;
export const addSerializer = cjsModule.addSerializer;
export const buildSnapshotResolver = cjsModule.buildSnapshotResolver;
export const cleanup = cjsModule.cleanup;
export const getSerializers = cjsModule.getSerializers;
export const isSnapshotPath = cjsModule.isSnapshotPath;
export const toMatchInlineSnapshot = cjsModule.toMatchInlineSnapshot;
export const toMatchSnapshot = cjsModule.toMatchSnapshot;
export const toThrowErrorMatchingInlineSnapshot = cjsModule.toThrowErrorMatchingInlineSnapshot;
export const toThrowErrorMatchingSnapshot = cjsModule.toThrowErrorMatchingSnapshot;
