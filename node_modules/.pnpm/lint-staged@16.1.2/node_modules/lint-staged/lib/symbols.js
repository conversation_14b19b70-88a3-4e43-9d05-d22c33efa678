export const ApplyEmptyCommitError = Symbol('ApplyEmptyCommitError')

export const ConfigNotFoundError = new Error('Configuration could not be found')

export const ConfigFormatError = new Error('Configuration should be an object or a function')

export const ConfigEmptyError = new Error('Configuration should not be empty')

export const GetBackupStashError = Symbol('GetBackupStashError')

export const GetStagedFilesError = Symbol('GetStagedFilesError')

export const GitError = Symbol('GitError')

export const GitRepoError = Symbol('GitRepoError')

export const HideUnstagedChangesError = Symbol('HideUnstagedChangesError')

export const InvalidOptionsError = new Error('Invalid Options')

export const RestoreMergeStatusError = Symbol('RestoreMergeStatusError')

export const RestoreOriginalStateError = Symbol('RestoreOriginalStateError')

export const RestoreUnstagedChangesError = Symbol('RestoreUnstagedChangesError')

export const TaskError = Symbol('TaskError')
