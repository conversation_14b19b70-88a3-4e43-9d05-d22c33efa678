{"version": 3, "file": "examples.js", "sourceRoot": "", "sources": ["../examples.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,iCAA+D;AAE/D,IAAM,OAAO,GAAG,EAAE,CAAA;AAClB,IAAM,IAAI,qBAAO,KAAK,CAAC,OAAO,CAAC,OAAC,CAAA;AAEhC,OAAO,CAAC,IAAI,CAAC,qCAAmC,IAAA,gBAAQ,GAAE,CAAC,cAAc,EAAI,CAAC,CAAA;AAC9E,OAAO,CAAC,IAAI,CAAC,mCAAiC,IAAA,iBAAS,GAAE,CAAC,cAAc,EAAI,CAAC,CAAA;AAC7E,OAAO,CAAC,IAAI,CAAC,mCAAiC,IAAA,iBAAS,GAAE,CAAC,cAAc,EAAI,CAAC,CAAA;AAC7E,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;AAC3B,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,GAAG,CAAC,OAAK,IAAA,eAAO,GAAI,CAAC,EAA7B,CAA6B,CAAC,CAAA;AAEhD,OAAO,CAAC,IAAI,CAAC,2DAAuD,CAAC,CAAA;AACrE,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,GAAG,CAAC,OAAK,IAAA,eAAO,EAAC,GAAG,CAAG,CAAC,EAAhC,CAAgC,CAAC,CAAA;AAEnD,OAAO,CAAC,IAAI,CAAC,4DAA0D,CAAC,CAAA;AACxE,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,GAAG,CAAC,OAAK,IAAA,eAAO,EAAC,KAAK,CAAG,CAAC,EAAlC,CAAkC,CAAC,CAAA;AAErD,IAAM,OAAO,GAAG,EAAE,cAAc,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,CAAA;AACtE,OAAO,CAAC,IAAI,CAAC,uBAAqB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAG,CAAC,CAAA;AACvE,OAAO,CAAC,IAAI,CAAC,mCAAiC,IAAA,gBAAQ,EAAC,OAAO,CAAC,CAAC,cAAc,EAAI,CAAC,CAAA;AACnF,OAAO,CAAC,IAAI,CAAC,mCAAiC,IAAA,iBAAS,EAAC,OAAO,CAAC,CAAC,cAAc,EAAI,CAAC,CAAA;AACpF,OAAO,CAAC,IAAI,CAAC,mCAAiC,IAAA,iBAAS,EAAC,OAAO,CAAC,CAAC,cAAc,EAAI,CAAC,CAAA;AACpF,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;AAChC,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,GAAG,CAAC,OAAK,IAAA,eAAO,EAAC,OAAO,CAAG,CAAC,EAApC,CAAoC,CAAC,CAAA"}