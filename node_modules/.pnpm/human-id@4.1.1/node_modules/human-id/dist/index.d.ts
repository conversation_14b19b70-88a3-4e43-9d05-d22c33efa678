export declare const adjectives: string[];
export declare const nouns: string[];
export declare const verbs: string[];
export declare const adverbs: string[];
export interface Options {
    separator?: string;
    capitalize?: boolean;
    adjectiveCount?: number;
    addAdverb?: boolean;
}
export declare function humanId(options?: Options | string | boolean): string;
export declare function poolSize(options?: Options): number;
export declare function maxLength(options?: Options): number;
export declare function minLength(options?: Options): number;
export default humanId;
