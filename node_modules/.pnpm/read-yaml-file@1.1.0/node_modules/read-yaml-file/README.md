# read-yaml-file

> Read and parse a YAML file

[![npm version](https://img.shields.io/npm/v/read-yaml-file.svg)](https://www.npmjs.com/package/read-yaml-file)

## Installation

```sh
npm install --save read-yaml-file
```

## Usage

```js
const readYamlFile = require('read-yaml-file')

readYamlFile('foo.yml').then(data => {
  console.log(data)
  //=> {foo: true}
})
```

## API

### readYamlFile(filepath)

Returns a promise for the parsed YAML.

### readYamlFile.sync(filepath)

Returns the parsed YAML.

## Related

- [write-yaml-file](https://github.com/zkochan/packages/tree/master/write-yaml-file) - Stringify and write YAML to a file atomically

## License

[MIT](./LICENSE) © [<PERSON><PERSON><PERSON>](https://www.kochan.io)

***

This package was forked from [load-yaml-file](https://github.com/LinusU/load-yaml-file)
