{"name": "@changesets/get-dependents-graph", "version": "2.1.3", "description": "Get the graph of dependents in a monorepo", "main": "dist/changesets-get-dependents-graph.cjs.js", "module": "dist/changesets-get-dependents-graph.esm.js", "exports": {".": {"types": {"import": "./dist/changesets-get-dependents-graph.cjs.mjs", "default": "./dist/changesets-get-dependents-graph.cjs.js"}, "module": "./dist/changesets-get-dependents-graph.esm.js", "import": "./dist/changesets-get-dependents-graph.cjs.mjs", "default": "./dist/changesets-get-dependents-graph.cjs.js"}, "./package.json": "./package.json"}, "license": "MIT", "repository": "https://github.com/changesets/changesets/tree/main/packages/get-dependents-graph", "dependencies": {"@changesets/types": "^6.1.0", "@manypkg/get-packages": "^1.1.3", "picocolors": "^1.1.0", "semver": "^7.5.3"}, "devDependencies": {"@changesets/test-utils": "*"}}