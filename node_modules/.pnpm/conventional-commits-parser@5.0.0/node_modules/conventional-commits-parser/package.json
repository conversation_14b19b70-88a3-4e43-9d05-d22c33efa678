{"name": "conventional-commits-parser", "version": "5.0.0", "description": "Parse raw conventional commits", "bugs": {"url": "https://github.com/conventional-changelog/conventional-changelog/issues"}, "homepage": "https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-commits-parser#readme", "author": {"name": "<PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com", "url": "https://github.com/stevemao"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/conventional-changelog.git"}, "license": "MIT", "engines": {"node": ">=16"}, "files": ["index.js", "cli.mjs", "lib"], "keywords": ["conventional-commits-parser", "changelog", "conventional", "parser", "parsing", "logs"], "dependencies": {"JSONStream": "^1.3.5", "is-text-path": "^2.0.0", "meow": "^12.0.1", "split2": "^4.0.0"}, "bin": {"conventional-commits-parser": "cli.mjs"}, "devDependencies": {"forceable-tty": "^0.1.0"}}