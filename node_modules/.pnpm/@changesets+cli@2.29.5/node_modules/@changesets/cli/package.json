{"name": "@changesets/cli", "version": "2.29.5", "description": "Organise your package versioning and publishing to make both contributors and maintainers happy", "bin": {"changeset": "bin.js"}, "repository": "https://github.com/changesets/changesets/tree/main/packages/cli", "files": ["default-files", "dist", "bin.js", "changelog", "commit"], "main": "dist/changesets-cli.cjs.js", "module": "dist/changesets-cli.esm.js", "exports": {".": {"types": {"import": "./dist/changesets-cli.cjs.mjs", "default": "./dist/changesets-cli.cjs.js"}, "module": "./dist/changesets-cli.esm.js", "import": "./dist/changesets-cli.cjs.mjs", "default": "./dist/changesets-cli.cjs.js"}, "./changelog": {"types": {"import": "./changelog/dist/changesets-cli-changelog.cjs.mjs", "default": "./changelog/dist/changesets-cli-changelog.cjs.js"}, "module": "./changelog/dist/changesets-cli-changelog.esm.js", "import": "./changelog/dist/changesets-cli-changelog.cjs.mjs", "default": "./changelog/dist/changesets-cli-changelog.cjs.js"}, "./commit": {"types": {"import": "./commit/dist/changesets-cli-commit.cjs.mjs", "default": "./commit/dist/changesets-cli-commit.cjs.js"}, "module": "./commit/dist/changesets-cli-commit.esm.js", "import": "./commit/dist/changesets-cli-commit.cjs.mjs", "default": "./commit/dist/changesets-cli-commit.cjs.js"}, "./package.json": "./package.json", "./bin.js": "./bin.js"}, "author": "Changesets Contributors", "contributors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/Andarist)"], "preconstruct": {"entrypoints": ["./index.ts", "./changelog.ts", "./commit/index.ts"], "exports": {"extra": {"./bin.js": "./bin.js"}}}, "license": "MIT", "dependencies": {"@changesets/apply-release-plan": "^7.0.12", "@changesets/assemble-release-plan": "^6.0.9", "@changesets/changelog-git": "^0.2.1", "@changesets/config": "^3.1.1", "@changesets/errors": "^0.2.0", "@changesets/get-dependents-graph": "^2.1.3", "@changesets/get-release-plan": "^4.0.13", "@changesets/git": "^3.0.4", "@changesets/logger": "^0.1.1", "@changesets/pre": "^2.0.2", "@changesets/read": "^0.6.5", "@changesets/should-skip-package": "^0.1.2", "@changesets/types": "^6.1.0", "@changesets/write": "^0.4.0", "@manypkg/get-packages": "^1.1.3", "ansi-colors": "^4.1.3", "ci-info": "^3.7.0", "enquirer": "^2.4.1", "external-editor": "^3.1.0", "fs-extra": "^7.0.1", "mri": "^1.2.0", "p-limit": "^2.2.0", "package-manager-detector": "^0.2.0", "picocolors": "^1.1.0", "resolve-from": "^5.0.0", "semver": "^7.5.3", "spawndamnit": "^3.0.1", "term-size": "^2.1.0"}, "devDependencies": {"@changesets/parse": "*", "@changesets/test-utils": "*", "@types/semver": "^7.5.0", "human-id": "^4.1.1", "outdent": "^0.5.0", "strip-ansi": "^5.2.0"}}