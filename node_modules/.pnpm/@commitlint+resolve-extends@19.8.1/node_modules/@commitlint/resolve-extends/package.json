{"name": "@commitlint/resolve-extends", "type": "module", "version": "19.8.1", "description": "Lint your commit messages", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/"], "scripts": {"deps": "dep-check", "pkg": "pkg-check"}, "engines": {"node": ">=v18"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/resolve-extends"}, "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "keywords": ["conventional-changelog", "commitlint", "library", "core"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/utils": "^19.8.1", "@types/lodash.mergewith": "^4.6.8"}, "dependencies": {"@commitlint/config-validator": "^19.8.1", "@commitlint/types": "^19.8.1", "global-directory": "^4.0.1", "import-meta-resolve": "^4.0.0", "lodash.mergewith": "^4.6.2", "resolve-from": "^5.0.0"}, "gitHead": "3c302008cabeb0b08cd246b2417a51a9d745a918"}