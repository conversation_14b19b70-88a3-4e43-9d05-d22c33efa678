{"lastValidatedTimestamp": 1751224516016, "projects": {"/Users/<USER>/projects/mcp-server-framework": {"name": "mcp-server-framework", "version": "0.0.0"}, "/Users/<USER>/projects/mcp-server-framework/packages/core": {"name": "@mcp-server-framework/core", "version": "0.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/*"]}, "filteredInstall": true}