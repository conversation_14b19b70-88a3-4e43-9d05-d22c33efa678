{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["packages/**/*.ts", "packages/**/*.tsx", "packages/**/*.js", "packages/**/*.jsx", "packages/**/*.json", "*.ts", "*.js", "*.json"]}, "formatter": {"enabled": true, "indentStyle": "tab", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noExtraBooleanCast": "error", "noUselessCatch": "error", "noUselessConstructor": "error", "noUselessLoneBlockStatements": "error", "noUselessRename": "error", "noUselessSwitchCase": "error", "noUselessTernary": "error"}, "correctness": {"noChildrenProp": "error", "noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidConstructorSuper": "error", "noInvalidBuiltinInstantiation": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedVariables": "error", "useIsNan": "error", "useValidForDirection": "error", "useYield": "error"}, "security": {"noDangerouslySetInnerHtml": "error", "noGlobalEval": "error"}, "style": {"noDefaultExport": "off", "noImplicitBoolean": "error", "noInferrableTypes": "error", "noNamespace": "error", "noNegationElse": "error", "noNonNullAssertion": "warn", "noParameterAssign": "error", "noRestrictedGlobals": "error", "noShoutyConstants": "error", "noUnusedTemplateLiteral": "error", "noUselessElse": "error", "useAsConstAssertion": "error", "useBlockStatements": "error", "useCollapsedElseIf": "error", "useConst": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useExponentiationOperator": "error", "useFragmentSyntax": "error", "useLiteralEnumMembers": "error", "useNamingConvention": {"level": "error", "options": {"strictCase": false, "conventions": [{"selector": {"kind": "function"}, "formats": ["camelCase", "PascalCase"]}, {"selector": {"kind": "variable"}, "formats": ["camelCase", "PascalCase", "CONSTANT_CASE"]}, {"selector": {"kind": "typeLike"}, "formats": ["PascalCase"]}]}}, "useSelfClosingElements": "error", "useShorthandAssign": "error", "useSingleVarDeclarator": "error", "useTemplate": "error"}, "suspicious": {"noArrayIndexKey": "error", "noAssignInExpressions": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCommentText": "error", "noCompareNegZero": "error", "noConfusingLabels": "error", "noConsole": "warn", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDoubleEquals": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noExplicitAny": "warn", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noLabelVar": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useGetterReturn": "error"}}}, "javascript": {"formatter": {"quoteStyle": "double", "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false}, "globals": ["jest", "describe", "test", "it", "expect", "beforeEach", "after<PERSON>ach", "beforeAll", "afterAll"]}, "json": {"formatter": {"trailingCommas": "none"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}, "overrides": [{"includes": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "linter": {"rules": {"suspicious": {"noExplicitAny": "off", "noConsole": "off"}}}}, {"includes": ["**/jest.config.js", "**/jest.config.ts"], "linter": {"rules": {"style": {"noDefaultExport": "off"}}}}]}