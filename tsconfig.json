{"compilerOptions": {"target": "ES2022", "module": "Node16", "lib": ["ES2022"], "moduleResolution": "Node16", "outDir": "./dist", "rootDir": "./packages", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "importHelpers": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "preserveWatchOutput": true, "pretty": true, "newLine": "lf", "stripInternal": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@mcp-server-framework/core": ["packages/core/src"], "@mcp-server-framework/core/*": ["packages/core/src/*"], "@mcp-server-framework/*": ["packages/*/src"]}}, "include": ["packages/**/*.ts", "packages/**/*.tsx", "packages/**/*.js", "packages/**/*.jsx", "packages/**/*.json"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/__tests__/**", "**/__mocks__/**", "**/coverage/**"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}