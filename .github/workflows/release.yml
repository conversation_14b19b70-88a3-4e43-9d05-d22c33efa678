name: Release

# This workflow creates version bump pull requests when changes are pushed to main.
# It does NOT publish packages to npm automatically - publishing is disabled until MVP completion.
#
# Current behavior:
# 1. Runs quality checks (linting, type checking, tests)
# 2. Creates "Version Packages" pull requests with changelog updates
# 3. When version PR is merged, packages are versioned but NOT published
#
# To enable publishing after MVP completion:
# 1. Add NPM_TOKEN to repository secrets
# 2. Update the changesets action to include publish step
# 3. Update version-packages script to include publishing

on:
  push:
    branches:
      - main

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Create Release PR
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PNPM
        uses: pnpm/action-setup@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Run linting
        run: pnpm run lint

      - name: Type check
        run: pnpm exec tsc --noEmit

      - name: Type check tests
        run: pnpm exec tsc --project tsconfig.test.json --noEmit

      - name: Run tests
        run: pnpm run test

      # Create version bump PRs without publishing to npm
      # Publishing will be enabled after MVP completion
      - name: Create Release Pull Request
        id: changesets
        uses: changesets/action@v1
        with:
          version: changeset version
          commit: "chore: version packages"
          title: "chore: version packages"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # NPM_TOKEN not needed since we're not publishing yet
          # Will be added when ready for public releases after MVP completion
